# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
- `php artisan serve` - Start development server
- `npm run dev` - Start Vite development server with hot reload
- `npm run build` - Build assets for production

### Testing
- `php artisan test` - Run all tests using Pest PHP
- `php artisan test --filter=TestName` - Run specific test
- `vendor/bin/pest` - Alternative way to run Pest tests
- `vendor/bin/pest tests/Feature` - Run only feature tests
- `vendor/bin/pest tests/Unit` - Run only unit tests

### Code Quality
- `vendor/bin/pint` - Format code using Laravel Pint
- `php artisan optimize` - Optimize the framework for production
- `php artisan config:clear` - Clear configuration cache

### Database
- `php artisan migrate` - Run database migrations
- `php artisan migrate:refresh --seed` - Refresh migrations and run seeders
- `php artisan db:seed` - Run database seeders

## Architecture Overview

### Livewire + Volt Integration
This project uses **Livewire Volt**, a single-file component system that combines PHP logic and Blade templates. Key patterns:

- **Volt Components**: Located in `resources/views/livewire/pages/` - these are full-page components using anonymous classes
- **Traditional Livewire**: Located in `app/Livewire/` - includes forms (`Forms/LoginForm.php`) and actions (`Actions/Logout.php`)
- **Volt Mounting**: Configured in `VoltServiceProvider.php` to mount both `livewire` and `pages` directories

### Authentication Architecture
- **Laravel Breeze**: Provides the authentication scaffolding
- **Livewire Forms**: Authentication logic is handled through `LoginForm` class with validation attributes
- **Volt Routes**: Authentication routes in `routes/auth.php` use `Volt::route()` instead of standard Route facade
- **Rate Limiting**: Implemented in LoginForm with IP-based throttling (5 attempts)

### Frontend Structure
- **Tailwind CSS**: Utility-first styling framework
- **Vite**: Modern build tool replacing Laravel Mix
- **Blade Components**: Reusable UI components in `resources/views/components/`
- **Layouts**: Two main layouts - `app.blade.php` (authenticated) and `guest.blade.php` (public)

### Key Directories
- `app/Livewire/`: Traditional Livewire components (forms, actions)
- `resources/views/livewire/pages/`: Volt single-file page components
- `resources/views/components/`: Reusable Blade components
- `tests/Feature/Auth/`: Comprehensive authentication test coverage

### Testing Configuration
- **Pest PHP**: Modern testing framework, configured in `tests/Pest.php`
- **Feature Tests**: Use `RefreshDatabase` trait automatically
- **Test Environment**: Configured in `phpunit.xml` with array drivers for performance